import React from 'react';
import { X } from 'lucide-react';
import { CloudStorageManager } from '@/components/CloudStorage';

interface CloudStorageModalProps {
  isOpen: boolean;
  onClose: () => void;
  syncStatus: any;
  userIdentity: any;
  lastModified: string;
  onForceSyncToCloud: () => Promise<boolean>;
  onForceLoadFromCloud: () => Promise<boolean>;
  onCreateBackup: () => Promise<any>;
  onRestoreFromBackup: (backup: any) => Promise<boolean>;
  onDeleteCloudData: () => Promise<boolean>;
  onSetCustomUserId: (userId: string) => Promise<boolean>;
  onGetUserStats: () => Promise<any>;
}

export function CloudStorageModal({
  isOpen,
  onClose,
  syncStatus,
  userIdentity,
  lastModified,
  onForceSyncToCloud,
  onForceLoadFromCloud,
  onCreateBackup,
  onRestoreFromBackup,
  onDeleteCloudData,
  onSetCustomUserId,
  onGetUserStats,
}: CloudStorageModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">云端存储管理</h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-50"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="p-6">
          <CloudStorageManager
            syncStatus={syncStatus}
            userIdentity={userIdentity}
            lastModified={lastModified}
            onForceSyncToCloud={onForceSyncToCloud}
            onForceLoadFromCloud={onForceLoadFromCloud}
            onCreateBackup={onCreateBackup}
            onRestoreFromBackup={onRestoreFromBackup}
            onDeleteCloudData={onDeleteCloudData}
            onSetCustomUserId={onSetCustomUserId}
            onGetUserStats={onGetUserStats}
          />
        </div>
      </div>
    </div>
  );
}
