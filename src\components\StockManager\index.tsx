import { useState } from 'react';
import { Settings, CheckCircle, XCircle, Activity, ActivitySquare, Cloud } from 'lucide-react';
import { useStockList } from '@/hooks/useStockList';
import { StockInput } from './StockInput';
import { StockList } from './StockList';
import { CloudStorageManager } from '@/components/CloudStorage';
import { CloudStorageModal } from './CloudStorageModal';

interface StockManagerProps {
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  isFullScreen?: boolean;
}

export function StockManager({ onSelectStock, selectedStock, isFullScreen = false }: StockManagerProps) {
  const {
    stocks,
    addStock,
    removeStock,
    isLoading,
    error,
    // 云端存储相关
    syncStatus,
    userIdentity,
    lastModified,
    forceSyncToCloud,
    forceLoadFromCloud,
    createBackup,
    restoreFromBackup,
    deleteCloudData,
    setCustomUserId,
    getUserStats,
  } = useStockList();
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);
  const [showRealTimeData, setShowRealTimeData] = useState(true);
  const [showCloudModal, setShowCloudModal] = useState(false);

  // 显示通知
  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 3000);
  };

  // 处理添加股票
  const handleAddStock = async (code: string, name?: string) => {
    const result = await addStock(code, name);

    if (result.success) {
      showNotification('success', result.message || '股票添加成功');
    } else {
      showNotification('error', result.message || '添加失败');
    }

    return result;
  };

  // 处理删除股票
  const handleRemoveStock = (code: string) => {
    removeStock(code);
    showNotification('success', '股票删除成功');
    
    // 如果删除的是当前选中的股票，清除选中状态
    if (selectedStock === code && onSelectStock) {
      onSelectStock('');
    }
  };

  return (
    <div className={`${isFullScreen ? 'w-full h-full bg-white overflow-hidden flex flex-col' : 'card p-6 h-full'}`}>
      {/* 头部 - 重新设计为单行布局 */}
      <div className={`flex items-center justify-between ${isFullScreen ? 'p-6 pb-4' : 'mb-6'}`}>
        {/* 左侧：标题 */}
        <div className="flex-shrink-0">
          <h2 className={`${isFullScreen ? 'text-3xl' : 'text-xl'} font-semibold text-gray-900`}>
            股票管理 {showRealTimeData ? '实时监控股票资金流向' : '添加和管理要监控的股票代码'}
          </h2>
        </div>

        {/* 右侧：股票输入、云端存储按钮、控制按钮 */}
        <div className="flex items-center gap-3">
          {/* 紧凑的股票输入 */}
          <StockInput
            onAddStock={handleAddStock}
            isLoading={isLoading}
            isFullScreen={isFullScreen}
            compact={true}
          />

          {/* 云端存储按钮 */}
          <button
            onClick={() => setShowCloudModal(true)}
            className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-50 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
            title="云端存储管理"
          >
            <Cloud className="w-4 h-4" />
            云端存储
          </button>

          {/* 实时监控开关 */}
          <button
            onClick={() => setShowRealTimeData(!showRealTimeData)}
            className={`p-2 rounded-lg transition-colors ${
              showRealTimeData
                ? 'text-blue-600 bg-blue-50 hover:bg-blue-100'
                : 'text-gray-400 hover:text-gray-600 hover:bg-gray-50'
            }`}
            title={showRealTimeData ? '关闭实时监控' : '开启实时监控'}
          >
            {showRealTimeData ? (
              <Activity className={`${isFullScreen ? 'w-6 h-6' : 'w-5 h-5'}`} />
            ) : (
              <ActivitySquare className={`${isFullScreen ? 'w-6 h-6' : 'w-5 h-5'}`} />
            )}
          </button>

          <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
            <Settings className={`${isFullScreen ? 'w-6 h-6' : 'w-5 h-5'}`} />
          </button>
        </div>
      </div>

      {/* 通知栏 */}
      {notification && (
        <div className={`
          flex items-center gap-2 p-3 rounded-lg animate-slide-up
          ${isFullScreen ? 'mx-6 mb-4' : 'mb-4'}
          ${notification.type === 'success'
            ? 'bg-success-50 text-success-700 border border-success-200'
            : 'bg-danger-50 text-danger-700 border border-danger-200'
          }
        `}>
          {notification.type === 'success' ? (
            <CheckCircle className="w-4 h-4" />
          ) : (
            <XCircle className="w-4 h-4" />
          )}
          <span className="text-sm">{notification.message}</span>
        </div>
      )}

      {/* 错误提示 */}
      {error && (
        <div className={`flex items-center gap-2 p-3 rounded-lg bg-danger-50 text-danger-700 border border-danger-200 ${isFullScreen ? 'mx-6 mb-4' : 'mb-4'}`}>
          <XCircle className="w-4 h-4" />
          <span className="text-sm">{error}</span>
        </div>
      )}



      {/* 股票列表 */}
      <div className={`flex-1 ${isFullScreen ? 'mx-6 overflow-hidden' : ''}`}>
        <StockList
          stocks={stocks}
          onRemoveStock={handleRemoveStock}
          onSelectStock={onSelectStock}
          selectedStock={selectedStock}
          showRealTimeData={showRealTimeData}
          isFullScreen={isFullScreen}
        />
      </div>

      {/* 统计信息 */}
      {stocks.length > 0 && (
        <div className={`pt-4 border-t border-gray-200 ${isFullScreen ? 'mx-6 mt-6' : 'mt-6'}`}>
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <span>已添加 {stocks.length} 只股票</span>
              {showRealTimeData && (
                <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs">
                  实时监控
                </span>
              )}
            </div>
            <span>
              {selectedStock ? `当前选中: ${selectedStock}` : '请选择股票查看数据'}
            </span>
          </div>
        </div>
      )}

      {/* 云端存储弹窗 */}
      <CloudStorageModal
        isOpen={showCloudModal}
        onClose={() => setShowCloudModal(false)}
        syncStatus={syncStatus}
        userIdentity={userIdentity}
        lastModified={lastModified}
        onForceSyncToCloud={forceSyncToCloud}
        onForceLoadFromCloud={forceLoadFromCloud}
        onCreateBackup={createBackup}
        onRestoreFromBackup={restoreFromBackup}
        onDeleteCloudData={deleteCloudData}
        onSetCustomUserId={setCustomUserId}
        onGetUserStats={getUserStats}
      />
    </div>
  );
}

export default StockManager;
